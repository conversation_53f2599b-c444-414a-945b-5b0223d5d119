import { createClient } from '@supabase/supabase-js';
import { Api<PERSON>eyGenerator } from './apiKeyGenerator';
import type { 
  UserGeneratedApiKey, 
  ApiKeyValidationResult, 
  RateLimitStatus 
} from '@/types/userApiKeys';

export class ApiKeyValidator {
  private supabase;

  constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
  }

  /**
   * Validates an API key and checks rate limits
   * @param apiKey The API key to validate
   * @param ipAddress The IP address making the request
   * @returns Validation result with key data and rate limit status
   */
  async validateApiKey(
    apiKey: string,
    ipAddress?: string
  ): Promise<ApiKeyValidationResult> {
    try {
      // First check format
      if (!ApiKeyGenerator.isValidFormat(apiKey)) {
        return {
          isValid: false,
          error: 'Invalid API key format'
        };
      }

      // Hash the key for lookup
      const keyHash = ApiKeyGenerator.hashApiKey(apiKey);

      // Look up the key in the database
      const { data: apiKeyData, error: keyError } = await this.supabase
        .from('user_generated_api_keys')
        .select(`
          *,
          custom_api_configs!inner(
            id,
            name,
            user_id,
            routing_strategy,
            routing_strategy_params
          )
        `)
        .eq('key_hash', keyHash)
        .eq('status', 'active')
        .single();

      if (keyError || !apiKeyData) {
        return {
          isValid: false,
          error: 'Invalid or inactive API key'
        };
      }

      // Check if key has expired
      if (apiKeyData.expires_at && new Date(apiKeyData.expires_at) < new Date()) {
        // Mark key as expired
        await this.supabase
          .from('user_generated_api_keys')
          .update({ status: 'expired' })
          .eq('id', apiKeyData.id);

        return {
          isValid: false,
          error: 'API key has expired'
        };
      }

      // Check IP restrictions
      if (apiKeyData.allowed_ips && apiKeyData.allowed_ips.length > 0 && ipAddress) {
        const isIpAllowed = this.checkIpAllowed(ipAddress, apiKeyData.allowed_ips);
        if (!isIpAllowed) {
          return {
            isValid: false,
            error: 'IP address not allowed for this API key'
          };
        }
      }

      // Check rate limits
      const rateLimitStatus = await this.checkRateLimits(apiKeyData.id);
      
      // Check if any rate limit is exceeded
      const rateLimitExceeded = 
        rateLimitStatus.minute.is_exceeded ||
        rateLimitStatus.hour.is_exceeded ||
        rateLimitStatus.day.is_exceeded;

      if (rateLimitExceeded) {
        return {
          isValid: false,
          error: 'Rate limit exceeded',
          rateLimitExceeded: true,
          rateLimitStatus
        };
      }

      // Update last used timestamp and IP
      await this.updateLastUsed(apiKeyData.id, ipAddress);

      return {
        isValid: true,
        apiKey: apiKeyData as UserGeneratedApiKey,
        rateLimitStatus
      };

    } catch (error) {
      console.error('Error validating API key:', error);
      return {
        isValid: false,
        error: 'Internal server error during validation'
      };
    }
  }

  /**
   * Checks if an IP address is allowed based on the allowed IPs list
   * @param ipAddress The IP address to check
   * @param allowedIps Array of allowed IP addresses/CIDR blocks
   * @returns True if IP is allowed
   */
  private checkIpAllowed(ipAddress: string, allowedIps: string[]): boolean {
    // For now, implement simple exact match
    // TODO: Add CIDR block support for more advanced IP filtering
    return allowedIps.includes(ipAddress) || allowedIps.includes('*');
  }

  /**
   * Checks rate limits for all time windows
   * @param apiKeyId The API key ID to check
   * @returns Rate limit status for all windows
   */
  private async checkRateLimits(apiKeyId: string): Promise<{
    minute: RateLimitStatus;
    hour: RateLimitStatus;
    day: RateLimitStatus;
  }> {
    const windows = ['minute', 'hour', 'day'] as const;
    const results: any = {};

    for (const window of windows) {
      const { data, error } = await this.supabase
        .rpc('get_rate_limit_status', {
          p_api_key_id: apiKeyId,
          p_window_type: window
        });

      if (error || !data || data.length === 0) {
        console.error(`Error checking ${window} rate limit:`, error);
        results[window] = {
          current_count: 0,
          limit_value: 0,
          window_start: new Date().toISOString(),
          is_exceeded: false
        };
      } else {
        results[window] = data[0];
      }
    }

    return results;
  }

  /**
   * Updates the last used timestamp and IP for an API key
   * @param apiKeyId The API key ID
   * @param ipAddress The IP address (optional)
   */
  private async updateLastUsed(apiKeyId: string, ipAddress?: string): Promise<void> {
    const updateData: any = {
      last_used_at: new Date().toISOString(),
      total_requests: this.supabase.raw('total_requests + 1')
    };

    if (ipAddress) {
      updateData.last_used_ip = ipAddress;
    }

    await this.supabase
      .from('user_generated_api_keys')
      .update(updateData)
      .eq('id', apiKeyId);
  }

  /**
   * Increments rate limit counters for an API key
   * @param apiKeyId The API key ID
   */
  async incrementRateLimitCounters(apiKeyId: string): Promise<void> {
    const windows = ['minute', 'hour', 'day'] as const;
    const now = new Date();

    for (const windowType of windows) {
      let windowStart: Date;
      
      switch (windowType) {
        case 'minute':
          windowStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours(), now.getMinutes());
          break;
        case 'hour':
          windowStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours());
          break;
        case 'day':
          windowStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
      }

      // Use upsert to increment or create the counter
      await this.supabase
        .from('user_api_key_rate_limits')
        .upsert({
          user_generated_api_key_id: apiKeyId,
          window_type: windowType,
          window_start: windowStart.toISOString(),
          request_count: 1
        }, {
          onConflict: 'user_generated_api_key_id,window_type,window_start',
          ignoreDuplicates: false
        });

      // If upsert doesn't work as expected, try manual increment
      const { data: existing } = await this.supabase
        .from('user_api_key_rate_limits')
        .select('request_count')
        .eq('user_generated_api_key_id', apiKeyId)
        .eq('window_type', windowType)
        .eq('window_start', windowStart.toISOString())
        .single();

      if (existing) {
        await this.supabase
          .from('user_api_key_rate_limits')
          .update({ 
            request_count: existing.request_count + 1,
            updated_at: new Date().toISOString()
          })
          .eq('user_generated_api_key_id', apiKeyId)
          .eq('window_type', windowType)
          .eq('window_start', windowStart.toISOString());
      }
    }
  }

  /**
   * Logs API usage for analytics and monitoring
   * @param apiKeyId The API key ID
   * @param userId The user ID
   * @param configId The custom API config ID
   * @param logData Usage log data
   */
  async logApiUsage(
    apiKeyId: string,
    userId: string,
    configId: string,
    logData: {
      endpoint: string;
      method: string;
      statusCode?: number;
      ipAddress?: string;
      userAgent?: string;
      referer?: string;
      modelUsed?: string;
      providerUsed?: string;
      tokensPrompt?: number;
      tokensCompletion?: number;
      costUsd?: number;
      responseTimeMs?: number;
      errorMessage?: string;
      errorType?: string;
    }
  ): Promise<void> {
    await this.supabase
      .from('user_api_key_usage_logs')
      .insert({
        user_generated_api_key_id: apiKeyId,
        user_id: userId,
        custom_api_config_id: configId,
        endpoint: logData.endpoint,
        http_method: logData.method,
        status_code: logData.statusCode,
        ip_address: logData.ipAddress,
        user_agent: logData.userAgent,
        referer: logData.referer,
        model_used: logData.modelUsed,
        provider_used: logData.providerUsed,
        tokens_prompt: logData.tokensPrompt,
        tokens_completion: logData.tokensCompletion,
        cost_usd: logData.costUsd,
        response_time_ms: logData.responseTimeMs,
        error_message: logData.errorMessage,
        error_type: logData.errorType
      });
  }
}
