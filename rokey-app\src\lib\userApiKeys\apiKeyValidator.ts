import { createClient } from '@supabase/supabase-js';
import { Api<PERSON>eyGenerator } from './apiKeyGenerator';
import type { 
  UserGeneratedApiKey, 
  ApiKeyValidationResult, 
  RateLimitStatus 
} from '@/types/userApiKeys';

export class ApiKeyValidator {
  private supabase;

  constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
  }

  /**
   * Validates an API key and checks rate limits
   * @param apiKey The API key to validate
   * @param ipAddress The IP address making the request
   * @returns Validation result with key data and rate limit status
   */
  async validateApiKey(
    apiKey: string,
    ipAddress?: string
  ): Promise<ApiKeyValidationResult> {
    try {
      // First check format
      if (!ApiKeyGenerator.isValidFormat(apiKey)) {
        return {
          isValid: false,
          error: 'Invalid API key format'
        };
      }

      // Hash the key for lookup
      const keyHash = ApiKeyGenerator.hashApiKey(apiKey);

      // Look up the key in the database
      const { data: apiKeyData, error: keyError } = await this.supabase
        .from('user_generated_api_keys')
        .select(`
          *,
          custom_api_configs!inner(
            id,
            name,
            user_id,
            routing_strategy,
            routing_strategy_params
          )
        `)
        .eq('key_hash', keyHash)
        .eq('status', 'active')
        .single();

      if (keyError || !apiKeyData) {
        return {
          isValid: false,
          error: 'Invalid or inactive API key'
        };
      }

      // Check if key has expired
      if (apiKeyData.expires_at && new Date(apiKeyData.expires_at) < new Date()) {
        // Mark key as expired
        await this.supabase
          .from('user_generated_api_keys')
          .update({ status: 'expired' })
          .eq('id', apiKeyData.id);

        return {
          isValid: false,
          error: 'API key has expired'
        };
      }

      // Check IP restrictions
      if (apiKeyData.allowed_ips && apiKeyData.allowed_ips.length > 0 && ipAddress) {
        const isIpAllowed = this.checkIpAllowed(ipAddress, apiKeyData.allowed_ips);
        if (!isIpAllowed) {
          return {
            isValid: false,
            error: 'IP address not allowed for this API key'
          };
        }
      }

      // Update last used timestamp and IP
      await this.updateLastUsed(apiKeyData.id, ipAddress);

      return {
        isValid: true,
        apiKey: apiKeyData as UserGeneratedApiKey
      };

    } catch (error) {
      console.error('Error validating API key:', error);
      return {
        isValid: false,
        error: 'Internal server error during validation'
      };
    }
  }

  /**
   * Checks if an IP address is allowed based on the allowed IPs list
   * @param ipAddress The IP address to check
   * @param allowedIps Array of allowed IP addresses/CIDR blocks
   * @returns True if IP is allowed
   */
  private checkIpAllowed(ipAddress: string, allowedIps: string[]): boolean {
    // For now, implement simple exact match
    // TODO: Add CIDR block support for more advanced IP filtering
    return allowedIps.includes(ipAddress) || allowedIps.includes('*');
  }



  /**
   * Updates the last used timestamp and IP for an API key
   * @param apiKeyId The API key ID
   * @param ipAddress The IP address (optional)
   */
  private async updateLastUsed(apiKeyId: string, ipAddress?: string): Promise<void> {
    const updateData: any = {
      last_used_at: new Date().toISOString(),
      total_requests: this.supabase.raw('total_requests + 1')
    };

    if (ipAddress) {
      updateData.last_used_ip = ipAddress;
    }

    await this.supabase
      .from('user_generated_api_keys')
      .update(updateData)
      .eq('id', apiKeyId);
  }



  /**
   * Logs API usage for analytics and monitoring
   * @param apiKeyId The API key ID
   * @param userId The user ID
   * @param configId The custom API config ID
   * @param logData Usage log data
   */
  async logApiUsage(
    apiKeyId: string,
    userId: string,
    configId: string,
    logData: {
      endpoint: string;
      method: string;
      statusCode?: number;
      ipAddress?: string;
      userAgent?: string;
      referer?: string;
      modelUsed?: string;
      providerUsed?: string;
      tokensPrompt?: number;
      tokensCompletion?: number;
      costUsd?: number;
      responseTimeMs?: number;
      errorMessage?: string;
      errorType?: string;
    }
  ): Promise<void> {
    await this.supabase
      .from('user_api_key_usage_logs')
      .insert({
        user_generated_api_key_id: apiKeyId,
        user_id: userId,
        custom_api_config_id: configId,
        endpoint: logData.endpoint,
        http_method: logData.method,
        status_code: logData.statusCode,
        ip_address: logData.ipAddress,
        user_agent: logData.userAgent,
        referer: logData.referer,
        model_used: logData.modelUsed,
        provider_used: logData.providerUsed,
        tokens_prompt: logData.tokensPrompt,
        tokens_completion: logData.tokensCompletion,
        cost_usd: logData.costUsd,
        response_time_ms: logData.responseTimeMs,
        error_message: logData.errorMessage,
        error_type: logData.errorType
      });
  }
}
