import crypto from 'crypto';
import { encrypt, decrypt } from '@/lib/encryption';

export class Api<PERSON>eyGenerator {
  private static readonly KEY_PREFIX = 'rk_live_';
  private static readonly RANDOM_PART_LENGTH = 8; // hex chars for middle part
  private static readonly SECRET_PART_LENGTH = 32; // chars for secret part

  /**
   * Generates a new API key with the format: rk_live_{8_hex_chars}_{32_random_chars}
   * @returns Object containing the full key, prefix, and secret parts
   */
  static generateApiKey(): {
    fullKey: string;
    prefix: string;
    secretPart: string;
    hash: string;
  } {
    // Generate random hex for the middle part (visible in prefix)
    const randomHex = crypto.randomBytes(this.RANDOM_PART_LENGTH / 2).toString('hex');
    
    // Generate random alphanumeric for the secret part
    const secretPart = this.generateRandomString(this.SECRET_PART_LENGTH);
    
    // Construct the full key
    const prefix = `${this.KEY_PREFIX}${randomHex}`;
    const fullKey = `${prefix}_${secretPart}`;
    
    // Generate hash for storage
    const hash = this.hashApiKey(fullKey);
    
    return {
      fullKey,
      prefix,
      secretPart,
      hash
    };
  }

  /**
   * Generates a cryptographically secure random string
   * @param length Length of the string to generate
   * @returns Random alphanumeric string
   */
  private static generateRandomString(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      const randomIndex = crypto.randomInt(0, chars.length);
      result += chars[randomIndex];
    }
    
    return result;
  }

  /**
   * Creates a SHA-256 hash of the API key for secure storage
   * @param apiKey The full API key to hash
   * @returns SHA-256 hash as hex string
   */
  static hashApiKey(apiKey: string): string {
    return crypto.createHash('sha256').update(apiKey).digest('hex');
  }

  /**
   * Validates the format of an API key
   * @param apiKey The API key to validate
   * @returns True if the format is valid
   */
  static isValidFormat(apiKey: string): boolean {
    const pattern = new RegExp(`^${this.KEY_PREFIX}[a-f0-9]{${this.RANDOM_PART_LENGTH}}_[a-zA-Z0-9]{${this.SECRET_PART_LENGTH}}$`);
    return pattern.test(apiKey);
  }

  /**
   * Extracts the prefix from a full API key
   * @param apiKey The full API key
   * @returns The prefix part (e.g., "rk_live_abc12345")
   */
  static extractPrefix(apiKey: string): string {
    const parts = apiKey.split('_');
    if (parts.length >= 3) {
      return `${parts[0]}_${parts[1]}_${parts[2]}`;
    }
    return '';
  }

  /**
   * Encrypts the suffix part of an API key for partial display
   * @param secretPart The secret part of the API key
   * @returns Encrypted suffix for storage
   */
  static encryptSuffix(secretPart: string): string {
    // Take last 4 characters for display purposes
    const suffix = secretPart.slice(-4);
    return encrypt(suffix);
  }

  /**
   * Decrypts the suffix for display
   * @param encryptedSuffix The encrypted suffix from database
   * @returns Decrypted suffix for display
   */
  static decryptSuffix(encryptedSuffix: string): string {
    try {
      return decrypt(encryptedSuffix);
    } catch (error) {
      console.error('Failed to decrypt API key suffix:', error);
      return '****';
    }
  }

  /**
   * Creates a masked version of the API key for display
   * @param prefix The key prefix
   * @param encryptedSuffix The encrypted suffix
   * @returns Masked key for display (e.g., "rk_live_abc12345_****xyz")
   */
  static createMaskedKey(prefix: string, encryptedSuffix: string): string {
    const suffix = this.decryptSuffix(encryptedSuffix);
    // Show 4 chars at the end, mask the rest (32 - 4 = 28 chars to mask)
    const maskedLength = this.SECRET_PART_LENGTH - 4;
    return `${prefix}_${'*'.repeat(maskedLength)}${suffix}`;
  }

  /**
   * Validates subscription tier limits for API key generation
   * @param subscriptionTier User's subscription tier
   * @param currentKeyCount Current number of API keys for the user
   * @returns Object indicating if generation is allowed and any limits
   */
  static validateSubscriptionLimits(
    subscriptionTier: string,
    currentKeyCount: number
  ): {
    allowed: boolean;
    limit: number;
    message?: string;
  } {
    const limits = {
      starter: 5,
      professional: 25,
      enterprise: 100
    };

    const limit = limits[subscriptionTier as keyof typeof limits] || limits.starter;

    if (currentKeyCount >= limit) {
      return {
        allowed: false,
        limit,
        message: `You have reached the maximum number of API keys (${limit}) for your ${subscriptionTier} plan.`
      };
    }

    return {
      allowed: true,
      limit
    };
  }

  /**
   * Gets default rate limits based on subscription tier
   * @param subscriptionTier User's subscription tier
   * @returns Default rate limits for the tier
   */
  static getDefaultRateLimits(subscriptionTier: string): {
    per_minute: number;
    per_hour: number;
    per_day: number;
  } {
    const rateLimits = {
      starter: {
        per_minute: 60,
        per_hour: 1500,
        per_day: 15000
      },
      professional: {
        per_minute: 500,
        per_hour: 15000,
        per_day: 200000
      },
      enterprise: {
        per_minute: 2000,
        per_hour: 60000,
        per_day: 1000000
      }
    };

    return rateLimits[subscriptionTier as keyof typeof rateLimits] || rateLimits.starter;
  }
}
