"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/EditApiKeyDialog.tsx":
/*!*********************************************************!*\
  !*** ./src/components/UserApiKeys/EditApiKeyDialog.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditApiKeyDialog: () => (/* binding */ EditApiKeyDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Plus,Settings,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Plus,Settings,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Plus,Settings,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Plus,Settings,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Plus,Settings,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ EditApiKeyDialog auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction EditApiKeyDialog(param) {\n    let { open, onOpenChange, apiKey, onUpdateApiKey } = param;\n    _s();\n    const [updating, setUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        key_name: '',\n        permissions: {\n            chat: true,\n            streaming: true,\n            all_models: true\n        },\n        allowed_ips: [],\n        allowed_domains: [],\n        status: 'active',\n        expires_at: ''\n    });\n    const [ipInput, setIpInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [domainInput, setDomainInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Initialize form data when apiKey changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditApiKeyDialog.useEffect\": ()=>{\n            if (apiKey) {\n                setFormData({\n                    key_name: apiKey.key_name || '',\n                    permissions: apiKey.permissions || {\n                        chat: true,\n                        streaming: true,\n                        all_models: true\n                    },\n                    allowed_ips: apiKey.allowed_ips || [],\n                    allowed_domains: apiKey.allowed_domains || [],\n                    status: apiKey.status || 'active',\n                    expires_at: apiKey.expires_at ? new Date(apiKey.expires_at).toISOString().slice(0, 16) : ''\n                });\n            }\n        }\n    }[\"EditApiKeyDialog.useEffect\"], [\n        apiKey\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.key_name.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error('Please enter a name for your API key');\n            return;\n        }\n        try {\n            setUpdating(true);\n            await onUpdateApiKey({\n                ...formData,\n                key_name: formData.key_name.trim(),\n                expires_at: formData.expires_at || null\n            });\n        } catch (error) {\n        // Error is handled in the parent component\n        } finally{\n            setUpdating(false);\n        }\n    };\n    const addIpAddress = ()=>{\n        if (ipInput.trim() && !formData.allowed_ips.includes(ipInput.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    allowed_ips: [\n                        ...prev.allowed_ips,\n                        ipInput.trim()\n                    ]\n                }));\n            setIpInput('');\n        }\n    };\n    const removeIpAddress = (ip)=>{\n        setFormData((prev)=>({\n                ...prev,\n                allowed_ips: prev.allowed_ips.filter((i)=>i !== ip)\n            }));\n    };\n    const addDomain = ()=>{\n        if (domainInput.trim() && !formData.allowed_domains.includes(domainInput.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    allowed_domains: [\n                        ...prev.allowed_domains,\n                        domainInput.trim()\n                    ]\n                }));\n            setDomainInput('');\n        }\n    };\n    const removeDomain = (domain)=>{\n        setFormData((prev)=>({\n                ...prev,\n                allowed_domains: prev.allowed_domains.filter((d)=>d !== domain)\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"max-w-2xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                \"Edit API Key\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                            children: \"Update the settings for your API key\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"key_name\",\n                                            children: \"API Key Name *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"key_name\",\n                                            value: formData.key_name,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        key_name: e.target.value\n                                                    })),\n                                            placeholder: \"e.g., Production API Key\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"status\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"status\",\n                                            value: formData.status,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        status: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"active\",\n                                                    children: \"Active\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"inactive\",\n                                                    children: \"Inactive\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            className: \"text-base font-semibold\",\n                                            children: \"Permissions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                    id: \"chat\",\n                                                    checked: formData.permissions.chat,\n                                                    onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                permissions: {\n                                                                    ...prev.permissions,\n                                                                    chat: !!checked\n                                                                }\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"chat\",\n                                                    className: \"text-sm\",\n                                                    children: \"Chat Completions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: \"Required\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                    id: \"streaming\",\n                                                    checked: formData.permissions.streaming,\n                                                    onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                permissions: {\n                                                                    ...prev.permissions,\n                                                                    streaming: !!checked\n                                                                }\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"streaming\",\n                                                    className: \"text-sm\",\n                                                    children: \"Streaming Responses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                    id: \"all_models\",\n                                                    checked: formData.permissions.all_models,\n                                                    onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                permissions: {\n                                                                    ...prev.permissions,\n                                                                    all_models: !!checked\n                                                                }\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"all_models\",\n                                                    className: \"text-sm\",\n                                                    children: \"Access to All Models\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            className: \"text-base font-semibold\",\n                                            children: \"Security Restrictions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            children: \"Allowed IP Addresses\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    value: ipInput,\n                                                    onChange: (e)=>setIpInput(e.target.value),\n                                                    placeholder: \"e.g., *********** or 10.0.0.0/8\",\n                                                    onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addIpAddress())\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    type: \"button\",\n                                                    onClick: addIpAddress,\n                                                    size: \"sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this),\n                                        formData.allowed_ips.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: formData.allowed_ips.map((ip)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        ip,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeIpAddress(ip),\n                                                            className: \"ml-1 h-3 w-3 p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-2 w-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, ip, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            children: \"Allowed Domains (CORS)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    value: domainInput,\n                                                    onChange: (e)=>setDomainInput(e.target.value),\n                                                    placeholder: \"e.g., example.com or *.example.com\",\n                                                    onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addDomain())\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    type: \"button\",\n                                                    onClick: addDomain,\n                                                    size: \"sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this),\n                                        formData.allowed_domains.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: formData.allowed_domains.map((domain)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        domain,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeDomain(domain),\n                                                            className: \"ml-1 h-3 w-3 p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-2 w-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, domain, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"expires_at\",\n                                        children: \"Expiration Date (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        id: \"expires_at\",\n                                        type: \"datetime-local\",\n                                        value: formData.expires_at,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    expires_at: e.target.value\n                                                })),\n                                        min: new Date().toISOString().slice(0, 16)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>onOpenChange(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"submit\",\n                                    disabled: updating,\n                                    children: updating ? 'Updating...' : 'Update API Key'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n            lineNumber: 133,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s(EditApiKeyDialog, \"SuclHHsvcs4TOMKffJq2XNuyEWA=\");\n_c = EditApiKeyDialog;\nvar _c;\n$RefreshReg$(_c, \"EditApiKeyDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1VzZXJBcGlLZXlzL0VkaXRBcGlLZXlEaWFsb2cudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFbUQ7QUFRbkI7QUFDZ0I7QUFDRjtBQUNBO0FBQ007QUFDTjtBQUNRO0FBT2hDO0FBQ1M7QUFTeEIsU0FBU3FCLGlCQUFpQixLQUtUO1FBTFMsRUFDL0JDLElBQUksRUFDSkMsWUFBWSxFQUNaQyxNQUFNLEVBQ05DLGNBQWMsRUFDUSxHQUxTOztJQU0vQixNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBRzFCLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQzJCLFVBQVVDLFlBQVksR0FBRzVCLCtDQUFRQSxDQUFDO1FBQ3ZDNkIsVUFBVTtRQUNWQyxhQUFhO1lBQ1hDLE1BQU07WUFDTkMsV0FBVztZQUNYQyxZQUFZO1FBQ2Q7UUFDQUMsYUFBYSxFQUFFO1FBQ2ZDLGlCQUFpQixFQUFFO1FBQ25CQyxRQUFRO1FBQ1JDLFlBQVk7SUFDZDtJQUVBLE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHdkMsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDd0MsYUFBYUMsZUFBZSxHQUFHekMsK0NBQVFBLENBQUM7SUFFL0MsMkNBQTJDO0lBQzNDQyxnREFBU0E7c0NBQUM7WUFDUixJQUFJc0IsUUFBUTtnQkFDVkssWUFBWTtvQkFDVkMsVUFBVU4sT0FBT00sUUFBUSxJQUFJO29CQUM3QkMsYUFBYVAsT0FBT08sV0FBVyxJQUFJO3dCQUNqQ0MsTUFBTTt3QkFDTkMsV0FBVzt3QkFDWEMsWUFBWTtvQkFDZDtvQkFDQUMsYUFBYVgsT0FBT1csV0FBVyxJQUFJLEVBQUU7b0JBQ3JDQyxpQkFBaUJaLE9BQU9ZLGVBQWUsSUFBSSxFQUFFO29CQUM3Q0MsUUFBUWIsT0FBT2EsTUFBTSxJQUFJO29CQUN6QkMsWUFBWWQsT0FBT2MsVUFBVSxHQUFHLElBQUlLLEtBQUtuQixPQUFPYyxVQUFVLEVBQUVNLFdBQVcsR0FBR0MsS0FBSyxDQUFDLEdBQUcsTUFBTTtnQkFDM0Y7WUFDRjtRQUNGO3FDQUFHO1FBQUNyQjtLQUFPO0lBRVgsTUFBTXNCLGVBQWUsT0FBT0M7UUFDMUJBLEVBQUVDLGNBQWM7UUFFaEIsSUFBSSxDQUFDcEIsU0FBU0UsUUFBUSxDQUFDbUIsSUFBSSxJQUFJO1lBQzdCN0IseUNBQUtBLENBQUM4QixLQUFLLENBQUM7WUFDWjtRQUNGO1FBRUEsSUFBSTtZQUNGdkIsWUFBWTtZQUNaLE1BQU1GLGVBQWU7Z0JBQ25CLEdBQUdHLFFBQVE7Z0JBQ1hFLFVBQVVGLFNBQVNFLFFBQVEsQ0FBQ21CLElBQUk7Z0JBQ2hDWCxZQUFZVixTQUFTVSxVQUFVLElBQUk7WUFDckM7UUFDRixFQUFFLE9BQU9ZLE9BQU87UUFDZCwyQ0FBMkM7UUFDN0MsU0FBVTtZQUNSdkIsWUFBWTtRQUNkO0lBQ0Y7SUFFQSxNQUFNd0IsZUFBZTtRQUNuQixJQUFJWixRQUFRVSxJQUFJLE1BQU0sQ0FBQ3JCLFNBQVNPLFdBQVcsQ0FBQ2lCLFFBQVEsQ0FBQ2IsUUFBUVUsSUFBSSxLQUFLO1lBQ3BFcEIsWUFBWXdCLENBQUFBLE9BQVM7b0JBQ25CLEdBQUdBLElBQUk7b0JBQ1BsQixhQUFhOzJCQUFJa0IsS0FBS2xCLFdBQVc7d0JBQUVJLFFBQVFVLElBQUk7cUJBQUc7Z0JBQ3BEO1lBQ0FULFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTWMsa0JBQWtCLENBQUNDO1FBQ3ZCMUIsWUFBWXdCLENBQUFBLE9BQVM7Z0JBQ25CLEdBQUdBLElBQUk7Z0JBQ1BsQixhQUFha0IsS0FBS2xCLFdBQVcsQ0FBQ3FCLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsTUFBTUY7WUFDbEQ7SUFDRjtJQUVBLE1BQU1HLFlBQVk7UUFDaEIsSUFBSWpCLFlBQVlRLElBQUksTUFBTSxDQUFDckIsU0FBU1EsZUFBZSxDQUFDZ0IsUUFBUSxDQUFDWCxZQUFZUSxJQUFJLEtBQUs7WUFDaEZwQixZQUFZd0IsQ0FBQUEsT0FBUztvQkFDbkIsR0FBR0EsSUFBSTtvQkFDUGpCLGlCQUFpQjsyQkFBSWlCLEtBQUtqQixlQUFlO3dCQUFFSyxZQUFZUSxJQUFJO3FCQUFHO2dCQUNoRTtZQUNBUCxlQUFlO1FBQ2pCO0lBQ0Y7SUFFQSxNQUFNaUIsZUFBZSxDQUFDQztRQUNwQi9CLFlBQVl3QixDQUFBQSxPQUFTO2dCQUNuQixHQUFHQSxJQUFJO2dCQUNQakIsaUJBQWlCaUIsS0FBS2pCLGVBQWUsQ0FBQ29CLE1BQU0sQ0FBQ0ssQ0FBQUEsSUFBS0EsTUFBTUQ7WUFDMUQ7SUFDRjtJQUVBLHFCQUNFLDhEQUFDekQseURBQU1BO1FBQUNtQixNQUFNQTtRQUFNQyxjQUFjQTtrQkFDaEMsNEVBQUNuQixnRUFBYUE7WUFBQzBELFdBQVU7OzhCQUN2Qiw4REFBQ3ZELCtEQUFZQTs7c0NBQ1gsOERBQUNDLDhEQUFXQTs0QkFBQ3NELFdBQVU7OzhDQUNyQiw4REFBQy9DLHlHQUFRQTtvQ0FBQytDLFdBQVU7Ozs7OztnQ0FBWTs7Ozs7OztzQ0FHbEMsOERBQUN6RCxvRUFBaUJBO3NDQUFDOzs7Ozs7Ozs7Ozs7OEJBS3JCLDhEQUFDMEQ7b0JBQUtDLFVBQVVsQjtvQkFBY2dCLFdBQVU7O3NDQUV0Qyw4REFBQ0c7NEJBQUlILFdBQVU7OzhDQUNiLDhEQUFDRztvQ0FBSUgsV0FBVTs7c0RBQ2IsOERBQUNuRCx1REFBS0E7NENBQUN1RCxTQUFRO3NEQUFXOzs7Ozs7c0RBQzFCLDhEQUFDeEQsdURBQUtBOzRDQUNKeUQsSUFBRzs0Q0FDSEMsT0FBT3hDLFNBQVNFLFFBQVE7NENBQ3hCdUMsVUFBVSxDQUFDdEIsSUFBTWxCLFlBQVl3QixDQUFBQSxPQUFTO3dEQUFFLEdBQUdBLElBQUk7d0RBQUV2QixVQUFVaUIsRUFBRXVCLE1BQU0sQ0FBQ0YsS0FBSztvREFBQzs0Q0FDMUVHLGFBQVk7NENBQ1pDLFFBQVE7Ozs7Ozs7Ozs7Ozs4Q0FJWiw4REFBQ1A7b0NBQUlILFdBQVU7O3NEQUNiLDhEQUFDbkQsdURBQUtBOzRDQUFDdUQsU0FBUTtzREFBUzs7Ozs7O3NEQUN4Qiw4REFBQ087NENBQ0NOLElBQUc7NENBQ0hDLE9BQU94QyxTQUFTUyxNQUFNOzRDQUN0QmdDLFVBQVUsQ0FBQ3RCLElBQU1sQixZQUFZd0IsQ0FBQUEsT0FBUzt3REFBRSxHQUFHQSxJQUFJO3dEQUFFaEIsUUFBUVUsRUFBRXVCLE1BQU0sQ0FBQ0YsS0FBSztvREFBMEI7NENBQ2pHTixXQUFVOzs4REFFViw4REFBQ1k7b0RBQU9OLE9BQU07OERBQVM7Ozs7Ozs4REFDdkIsOERBQUNNO29EQUFPTixPQUFNOzhEQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSy9CLDhEQUFDdEQsK0RBQVNBOzs7OztzQ0FHViw4REFBQ21EOzRCQUFJSCxXQUFVOzs4Q0FDYiw4REFBQ0c7b0NBQUlILFdBQVU7O3NEQUNiLDhEQUFDOUMseUdBQU1BOzRDQUFDOEMsV0FBVTs7Ozs7O3NEQUNsQiw4REFBQ25ELHVEQUFLQTs0Q0FBQ21ELFdBQVU7c0RBQTBCOzs7Ozs7Ozs7Ozs7OENBRTdDLDhEQUFDRztvQ0FBSUgsV0FBVTs7c0RBQ2IsOERBQUNHOzRDQUFJSCxXQUFVOzs4REFDYiw4REFBQ2xELDZEQUFRQTtvREFDUHVELElBQUc7b0RBQ0hRLFNBQVMvQyxTQUFTRyxXQUFXLENBQUNDLElBQUk7b0RBQ2xDNEMsaUJBQWlCLENBQUNELFVBQ2hCOUMsWUFBWXdCLENBQUFBLE9BQVM7Z0VBQ25CLEdBQUdBLElBQUk7Z0VBQ1B0QixhQUFhO29FQUFFLEdBQUdzQixLQUFLdEIsV0FBVztvRUFBRUMsTUFBTSxDQUFDLENBQUMyQztnRUFBUTs0REFDdEQ7Ozs7Ozs4REFHSiw4REFBQ2hFLHVEQUFLQTtvREFBQ3VELFNBQVE7b0RBQU9KLFdBQVU7OERBQVU7Ozs7Ozs4REFHMUMsOERBQUNqRCx1REFBS0E7b0RBQUNnRSxTQUFRO29EQUFZZixXQUFVOzhEQUFVOzs7Ozs7Ozs7Ozs7c0RBRWpELDhEQUFDRzs0Q0FBSUgsV0FBVTs7OERBQ2IsOERBQUNsRCw2REFBUUE7b0RBQ1B1RCxJQUFHO29EQUNIUSxTQUFTL0MsU0FBU0csV0FBVyxDQUFDRSxTQUFTO29EQUN2QzJDLGlCQUFpQixDQUFDRCxVQUNoQjlDLFlBQVl3QixDQUFBQSxPQUFTO2dFQUNuQixHQUFHQSxJQUFJO2dFQUNQdEIsYUFBYTtvRUFBRSxHQUFHc0IsS0FBS3RCLFdBQVc7b0VBQUVFLFdBQVcsQ0FBQyxDQUFDMEM7Z0VBQVE7NERBQzNEOzs7Ozs7OERBR0osOERBQUNoRSx1REFBS0E7b0RBQUN1RCxTQUFRO29EQUFZSixXQUFVOzhEQUFVOzs7Ozs7Ozs7Ozs7c0RBSWpELDhEQUFDRzs0Q0FBSUgsV0FBVTs7OERBQ2IsOERBQUNsRCw2REFBUUE7b0RBQ1B1RCxJQUFHO29EQUNIUSxTQUFTL0MsU0FBU0csV0FBVyxDQUFDRyxVQUFVO29EQUN4QzBDLGlCQUFpQixDQUFDRCxVQUNoQjlDLFlBQVl3QixDQUFBQSxPQUFTO2dFQUNuQixHQUFHQSxJQUFJO2dFQUNQdEIsYUFBYTtvRUFBRSxHQUFHc0IsS0FBS3RCLFdBQVc7b0VBQUVHLFlBQVksQ0FBQyxDQUFDeUM7Z0VBQVE7NERBQzVEOzs7Ozs7OERBR0osOERBQUNoRSx1REFBS0E7b0RBQUN1RCxTQUFRO29EQUFhSixXQUFVOzhEQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBVXRELDhEQUFDRzs0QkFBSUgsV0FBVTs7OENBQ2IsOERBQUNHO29DQUFJSCxXQUFVOztzREFDYiw4REFBQzlDLHlHQUFNQTs0Q0FBQzhDLFdBQVU7Ozs7OztzREFDbEIsOERBQUNuRCx1REFBS0E7NENBQUNtRCxXQUFVO3NEQUEwQjs7Ozs7Ozs7Ozs7OzhDQUk3Qyw4REFBQ0c7b0NBQUlILFdBQVU7O3NEQUNiLDhEQUFDbkQsdURBQUtBO3NEQUFDOzs7Ozs7c0RBQ1AsOERBQUNzRDs0Q0FBSUgsV0FBVTs7OERBQ2IsOERBQUNwRCx1REFBS0E7b0RBQ0owRCxPQUFPN0I7b0RBQ1A4QixVQUFVLENBQUN0QixJQUFNUCxXQUFXTyxFQUFFdUIsTUFBTSxDQUFDRixLQUFLO29EQUMxQ0csYUFBWTtvREFDWk8sWUFBWSxDQUFDL0IsSUFBTUEsRUFBRWdDLEdBQUcsS0FBSyxXQUFZaEMsQ0FBQUEsRUFBRUMsY0FBYyxJQUFJRyxjQUFhOzs7Ozs7OERBRTVFLDhEQUFDMUMseURBQU1BO29EQUFDdUUsTUFBSztvREFBU0MsU0FBUzlCO29EQUFjK0IsTUFBSzs4REFDaEQsNEVBQUNoRSx5R0FBSUE7d0RBQUM0QyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozt3Q0FHbkJsQyxTQUFTTyxXQUFXLENBQUNnRCxNQUFNLEdBQUcsbUJBQzdCLDhEQUFDbEI7NENBQUlILFdBQVU7c0RBQ1psQyxTQUFTTyxXQUFXLENBQUNpRCxHQUFHLENBQUMsQ0FBQzdCLG1CQUN6Qiw4REFBQzFDLHVEQUFLQTtvREFBVWdFLFNBQVE7b0RBQVlmLFdBQVU7O3dEQUMzQ1A7c0VBQ0QsOERBQUM5Qyx5REFBTUE7NERBQ0x1RSxNQUFLOzREQUNMSCxTQUFROzREQUNSSyxNQUFLOzREQUNMRCxTQUFTLElBQU0zQixnQkFBZ0JDOzREQUMvQk8sV0FBVTtzRUFFViw0RUFBQzNDLHlHQUFDQTtnRUFBQzJDLFdBQVU7Ozs7Ozs7Ozs7OzttREFUTFA7Ozs7Ozs7Ozs7Ozs7Ozs7OENBa0JwQiw4REFBQ1U7b0NBQUlILFdBQVU7O3NEQUNiLDhEQUFDbkQsdURBQUtBO3NEQUFDOzs7Ozs7c0RBQ1AsOERBQUNzRDs0Q0FBSUgsV0FBVTs7OERBQ2IsOERBQUNwRCx1REFBS0E7b0RBQ0owRCxPQUFPM0I7b0RBQ1A0QixVQUFVLENBQUN0QixJQUFNTCxlQUFlSyxFQUFFdUIsTUFBTSxDQUFDRixLQUFLO29EQUM5Q0csYUFBWTtvREFDWk8sWUFBWSxDQUFDL0IsSUFBTUEsRUFBRWdDLEdBQUcsS0FBSyxXQUFZaEMsQ0FBQUEsRUFBRUMsY0FBYyxJQUFJVSxXQUFVOzs7Ozs7OERBRXpFLDhEQUFDakQseURBQU1BO29EQUFDdUUsTUFBSztvREFBU0MsU0FBU3ZCO29EQUFXd0IsTUFBSzs4REFDN0MsNEVBQUNoRSx5R0FBSUE7d0RBQUM0QyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozt3Q0FHbkJsQyxTQUFTUSxlQUFlLENBQUMrQyxNQUFNLEdBQUcsbUJBQ2pDLDhEQUFDbEI7NENBQUlILFdBQVU7c0RBQ1psQyxTQUFTUSxlQUFlLENBQUNnRCxHQUFHLENBQUMsQ0FBQ3hCLHVCQUM3Qiw4REFBQy9DLHVEQUFLQTtvREFBY2dFLFNBQVE7b0RBQVlmLFdBQVU7O3NFQUNoRCw4REFBQzdDLHlHQUFLQTs0REFBQzZDLFdBQVU7Ozs7Ozt3REFDaEJGO3NFQUNELDhEQUFDbkQseURBQU1BOzREQUNMdUUsTUFBSzs0REFDTEgsU0FBUTs0REFDUkssTUFBSzs0REFDTEQsU0FBUyxJQUFNdEIsYUFBYUM7NERBQzVCRSxXQUFVO3NFQUVWLDRFQUFDM0MseUdBQUNBO2dFQUFDMkMsV0FBVTs7Ozs7Ozs7Ozs7O21EQVZMRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FtQnRCLDhEQUFDOUMsK0RBQVNBOzs7OztzQ0FHViw4REFBQ21EOzRCQUFJSCxXQUFVO3NDQUNiLDRFQUFDRztnQ0FBSUgsV0FBVTs7a0RBQ2IsOERBQUNuRCx1REFBS0E7d0NBQUN1RCxTQUFRO2tEQUFhOzs7Ozs7a0RBQzVCLDhEQUFDeEQsdURBQUtBO3dDQUNKeUQsSUFBRzt3Q0FDSGEsTUFBSzt3Q0FDTFosT0FBT3hDLFNBQVNVLFVBQVU7d0NBQzFCK0IsVUFBVSxDQUFDdEIsSUFBTWxCLFlBQVl3QixDQUFBQSxPQUFTO29EQUFFLEdBQUdBLElBQUk7b0RBQUVmLFlBQVlTLEVBQUV1QixNQUFNLENBQUNGLEtBQUs7Z0RBQUM7d0NBQzVFaUIsS0FBSyxJQUFJMUMsT0FBT0MsV0FBVyxHQUFHQyxLQUFLLENBQUMsR0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSzdDLDhEQUFDdkMsK0RBQVlBOzs4Q0FDWCw4REFBQ0cseURBQU1BO29DQUFDdUUsTUFBSztvQ0FBU0gsU0FBUTtvQ0FBVUksU0FBUyxJQUFNMUQsYUFBYTs4Q0FBUTs7Ozs7OzhDQUc1RSw4REFBQ2QseURBQU1BO29DQUFDdUUsTUFBSztvQ0FBU00sVUFBVTVEOzhDQUM3QkEsV0FBVyxnQkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzFDO0dBL1NnQkw7S0FBQUEiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxjb21wb25lbnRzXFxVc2VyQXBpS2V5c1xcRWRpdEFwaUtleURpYWxvZy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7XG4gIERpYWxvZyxcbiAgRGlhbG9nQ29udGVudCxcbiAgRGlhbG9nRGVzY3JpcHRpb24sXG4gIERpYWxvZ0Zvb3RlcixcbiAgRGlhbG9nSGVhZGVyLFxuICBEaWFsb2dUaXRsZSxcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2RpYWxvZyc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcbmltcG9ydCB7IElucHV0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2lucHV0JztcbmltcG9ydCB7IExhYmVsIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2xhYmVsJztcbmltcG9ydCB7IENoZWNrYm94IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NoZWNrYm94JztcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2JhZGdlJztcbmltcG9ydCB7IFNlcGFyYXRvciB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9zZXBhcmF0b3InO1xuaW1wb3J0IHtcbiAgU2V0dGluZ3MsXG4gIFNoaWVsZCxcbiAgR2xvYmUsXG4gIFBsdXMsXG4gIFhcbn0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSAnc29ubmVyJztcblxuaW50ZXJmYWNlIEVkaXRBcGlLZXlEaWFsb2dQcm9wcyB7XG4gIG9wZW46IGJvb2xlYW47XG4gIG9uT3BlbkNoYW5nZTogKG9wZW46IGJvb2xlYW4pID0+IHZvaWQ7XG4gIGFwaUtleTogYW55O1xuICBvblVwZGF0ZUFwaUtleTogKHVwZGF0ZXM6IGFueSkgPT4gUHJvbWlzZTx2b2lkPjtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIEVkaXRBcGlLZXlEaWFsb2coe1xuICBvcGVuLFxuICBvbk9wZW5DaGFuZ2UsXG4gIGFwaUtleSxcbiAgb25VcGRhdGVBcGlLZXlcbn06IEVkaXRBcGlLZXlEaWFsb2dQcm9wcykge1xuICBjb25zdCBbdXBkYXRpbmcsIHNldFVwZGF0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZSh7XG4gICAga2V5X25hbWU6ICcnLFxuICAgIHBlcm1pc3Npb25zOiB7XG4gICAgICBjaGF0OiB0cnVlLFxuICAgICAgc3RyZWFtaW5nOiB0cnVlLFxuICAgICAgYWxsX21vZGVsczogdHJ1ZVxuICAgIH0sXG4gICAgYWxsb3dlZF9pcHM6IFtdIGFzIHN0cmluZ1tdLFxuICAgIGFsbG93ZWRfZG9tYWluczogW10gYXMgc3RyaW5nW10sXG4gICAgc3RhdHVzOiAnYWN0aXZlJyBhcyAnYWN0aXZlJyB8ICdpbmFjdGl2ZScsXG4gICAgZXhwaXJlc19hdDogJydcbiAgfSk7XG5cbiAgY29uc3QgW2lwSW5wdXQsIHNldElwSW5wdXRdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbZG9tYWluSW5wdXQsIHNldERvbWFpbklucHV0XSA9IHVzZVN0YXRlKCcnKTtcblxuICAvLyBJbml0aWFsaXplIGZvcm0gZGF0YSB3aGVuIGFwaUtleSBjaGFuZ2VzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGFwaUtleSkge1xuICAgICAgc2V0Rm9ybURhdGEoe1xuICAgICAgICBrZXlfbmFtZTogYXBpS2V5LmtleV9uYW1lIHx8ICcnLFxuICAgICAgICBwZXJtaXNzaW9uczogYXBpS2V5LnBlcm1pc3Npb25zIHx8IHtcbiAgICAgICAgICBjaGF0OiB0cnVlLFxuICAgICAgICAgIHN0cmVhbWluZzogdHJ1ZSxcbiAgICAgICAgICBhbGxfbW9kZWxzOiB0cnVlXG4gICAgICAgIH0sXG4gICAgICAgIGFsbG93ZWRfaXBzOiBhcGlLZXkuYWxsb3dlZF9pcHMgfHwgW10sXG4gICAgICAgIGFsbG93ZWRfZG9tYWluczogYXBpS2V5LmFsbG93ZWRfZG9tYWlucyB8fCBbXSxcbiAgICAgICAgc3RhdHVzOiBhcGlLZXkuc3RhdHVzIHx8ICdhY3RpdmUnLFxuICAgICAgICBleHBpcmVzX2F0OiBhcGlLZXkuZXhwaXJlc19hdCA/IG5ldyBEYXRlKGFwaUtleS5leHBpcmVzX2F0KS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE2KSA6ICcnXG4gICAgICB9KTtcbiAgICB9XG4gIH0sIFthcGlLZXldKTtcblxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoZTogUmVhY3QuRm9ybUV2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIFxuICAgIGlmICghZm9ybURhdGEua2V5X25hbWUudHJpbSgpKSB7XG4gICAgICB0b2FzdC5lcnJvcignUGxlYXNlIGVudGVyIGEgbmFtZSBmb3IgeW91ciBBUEkga2V5Jyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIHNldFVwZGF0aW5nKHRydWUpO1xuICAgICAgYXdhaXQgb25VcGRhdGVBcGlLZXkoe1xuICAgICAgICAuLi5mb3JtRGF0YSxcbiAgICAgICAga2V5X25hbWU6IGZvcm1EYXRhLmtleV9uYW1lLnRyaW0oKSxcbiAgICAgICAgZXhwaXJlc19hdDogZm9ybURhdGEuZXhwaXJlc19hdCB8fCBudWxsXG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgLy8gRXJyb3IgaXMgaGFuZGxlZCBpbiB0aGUgcGFyZW50IGNvbXBvbmVudFxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRVcGRhdGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGFkZElwQWRkcmVzcyA9ICgpID0+IHtcbiAgICBpZiAoaXBJbnB1dC50cmltKCkgJiYgIWZvcm1EYXRhLmFsbG93ZWRfaXBzLmluY2x1ZGVzKGlwSW5wdXQudHJpbSgpKSkge1xuICAgICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgICAuLi5wcmV2LFxuICAgICAgICBhbGxvd2VkX2lwczogWy4uLnByZXYuYWxsb3dlZF9pcHMsIGlwSW5wdXQudHJpbSgpXVxuICAgICAgfSkpO1xuICAgICAgc2V0SXBJbnB1dCgnJyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHJlbW92ZUlwQWRkcmVzcyA9IChpcDogc3RyaW5nKSA9PiB7XG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIGFsbG93ZWRfaXBzOiBwcmV2LmFsbG93ZWRfaXBzLmZpbHRlcihpID0+IGkgIT09IGlwKVxuICAgIH0pKTtcbiAgfTtcblxuICBjb25zdCBhZGREb21haW4gPSAoKSA9PiB7XG4gICAgaWYgKGRvbWFpbklucHV0LnRyaW0oKSAmJiAhZm9ybURhdGEuYWxsb3dlZF9kb21haW5zLmluY2x1ZGVzKGRvbWFpbklucHV0LnRyaW0oKSkpIHtcbiAgICAgIHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgICAgLi4ucHJldixcbiAgICAgICAgYWxsb3dlZF9kb21haW5zOiBbLi4ucHJldi5hbGxvd2VkX2RvbWFpbnMsIGRvbWFpbklucHV0LnRyaW0oKV1cbiAgICAgIH0pKTtcbiAgICAgIHNldERvbWFpbklucHV0KCcnKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgcmVtb3ZlRG9tYWluID0gKGRvbWFpbjogc3RyaW5nKSA9PiB7XG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIGFsbG93ZWRfZG9tYWluczogcHJldi5hbGxvd2VkX2RvbWFpbnMuZmlsdGVyKGQgPT4gZCAhPT0gZG9tYWluKVxuICAgIH0pKTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxEaWFsb2cgb3Blbj17b3Blbn0gb25PcGVuQ2hhbmdlPXtvbk9wZW5DaGFuZ2V9PlxuICAgICAgPERpYWxvZ0NvbnRlbnQgY2xhc3NOYW1lPVwibWF4LXctMnhsIG1heC1oLVs5MHZoXSBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAgPERpYWxvZ0hlYWRlcj5cbiAgICAgICAgICA8RGlhbG9nVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgIDxTZXR0aW5ncyBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICAgIEVkaXQgQVBJIEtleVxuICAgICAgICAgIDwvRGlhbG9nVGl0bGU+XG4gICAgICAgICAgPERpYWxvZ0Rlc2NyaXB0aW9uPlxuICAgICAgICAgICAgVXBkYXRlIHRoZSBzZXR0aW5ncyBmb3IgeW91ciBBUEkga2V5XG4gICAgICAgICAgPC9EaWFsb2dEZXNjcmlwdGlvbj5cbiAgICAgICAgPC9EaWFsb2dIZWFkZXI+XG5cbiAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0gY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgey8qIEJhc2ljIEluZm9ybWF0aW9uICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImtleV9uYW1lXCI+QVBJIEtleSBOYW1lICo8L0xhYmVsPlxuICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICBpZD1cImtleV9uYW1lXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEua2V5X25hbWV9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIGtleV9uYW1lOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJlLmcuLCBQcm9kdWN0aW9uIEFQSSBLZXlcIlxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJzdGF0dXNcIj5TdGF0dXM8L0xhYmVsPlxuICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgaWQ9XCJzdGF0dXNcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5zdGF0dXN9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIHN0YXR1czogZS50YXJnZXQudmFsdWUgYXMgJ2FjdGl2ZScgfCAnaW5hY3RpdmUnIH0pKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImFjdGl2ZVwiPkFjdGl2ZTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJpbmFjdGl2ZVwiPkluYWN0aXZlPC9vcHRpb24+XG4gICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8U2VwYXJhdG9yIC8+XG5cbiAgICAgICAgICB7LyogUGVybWlzc2lvbnMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPFNoaWVsZCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cInRleHQtYmFzZSBmb250LXNlbWlib2xkXCI+UGVybWlzc2lvbnM8L0xhYmVsPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIDxDaGVja2JveFxuICAgICAgICAgICAgICAgICAgaWQ9XCJjaGF0XCJcbiAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2Zvcm1EYXRhLnBlcm1pc3Npb25zLmNoYXR9XG4gICAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9eyhjaGVja2VkKSA9PiBcbiAgICAgICAgICAgICAgICAgICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICAgICAgcGVybWlzc2lvbnM6IHsgLi4ucHJldi5wZXJtaXNzaW9ucywgY2hhdDogISFjaGVja2VkIH1cbiAgICAgICAgICAgICAgICAgICAgfSkpXG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImNoYXRcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICBDaGF0IENvbXBsZXRpb25zXG4gICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cInNlY29uZGFyeVwiIGNsYXNzTmFtZT1cInRleHQteHNcIj5SZXF1aXJlZDwvQmFkZ2U+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIDxDaGVja2JveFxuICAgICAgICAgICAgICAgICAgaWQ9XCJzdHJlYW1pbmdcIlxuICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Zm9ybURhdGEucGVybWlzc2lvbnMuc3RyZWFtaW5nfVxuICAgICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXsoY2hlY2tlZCkgPT4gXG4gICAgICAgICAgICAgICAgICAgIHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgICAgIHBlcm1pc3Npb25zOiB7IC4uLnByZXYucGVybWlzc2lvbnMsIHN0cmVhbWluZzogISFjaGVja2VkIH1cbiAgICAgICAgICAgICAgICAgICAgfSkpXG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInN0cmVhbWluZ1wiIGNsYXNzTmFtZT1cInRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgIFN0cmVhbWluZyBSZXNwb25zZXNcbiAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICA8Q2hlY2tib3hcbiAgICAgICAgICAgICAgICAgIGlkPVwiYWxsX21vZGVsc1wiXG4gICAgICAgICAgICAgICAgICBjaGVja2VkPXtmb3JtRGF0YS5wZXJtaXNzaW9ucy5hbGxfbW9kZWxzfVxuICAgICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXsoY2hlY2tlZCkgPT4gXG4gICAgICAgICAgICAgICAgICAgIHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgICAgIHBlcm1pc3Npb25zOiB7IC4uLnByZXYucGVybWlzc2lvbnMsIGFsbF9tb2RlbHM6ICEhY2hlY2tlZCB9XG4gICAgICAgICAgICAgICAgICAgIH0pKVxuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJhbGxfbW9kZWxzXCIgY2xhc3NOYW1lPVwidGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgQWNjZXNzIHRvIEFsbCBNb2RlbHNcbiAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG5cblxuICAgICAgICAgIHsvKiBTZWN1cml0eSBSZXN0cmljdGlvbnMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPFNoaWVsZCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cInRleHQtYmFzZSBmb250LXNlbWlib2xkXCI+U2VjdXJpdHkgUmVzdHJpY3Rpb25zPC9MYWJlbD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7LyogSVAgUmVzdHJpY3Rpb25zICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgPExhYmVsPkFsbG93ZWQgSVAgQWRkcmVzc2VzPC9MYWJlbD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICB2YWx1ZT17aXBJbnB1dH1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0SXBJbnB1dChlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImUuZy4sIDE5Mi4xNjguMS4xIG9yIDEwLjAuMC4wLzhcIlxuICAgICAgICAgICAgICAgICAgb25LZXlQcmVzcz17KGUpID0+IGUua2V5ID09PSAnRW50ZXInICYmIChlLnByZXZlbnREZWZhdWx0KCksIGFkZElwQWRkcmVzcygpKX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDxCdXR0b24gdHlwZT1cImJ1dHRvblwiIG9uQ2xpY2s9e2FkZElwQWRkcmVzc30gc2l6ZT1cInNtXCI+XG4gICAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIHtmb3JtRGF0YS5hbGxvd2VkX2lwcy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICB7Zm9ybURhdGEuYWxsb3dlZF9pcHMubWFwKChpcCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8QmFkZ2Uga2V5PXtpcH0gdmFyaWFudD1cInNlY29uZGFyeVwiIGNsYXNzTmFtZT1cInRleHQteHNcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7aXB9XG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJlbW92ZUlwQWRkcmVzcyhpcCl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtbC0xIGgtMyB3LTMgcC0wXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJoLTIgdy0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBEb21haW4gUmVzdHJpY3Rpb25zICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgPExhYmVsPkFsbG93ZWQgRG9tYWlucyAoQ09SUyk8L0xhYmVsPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtkb21haW5JbnB1dH1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RG9tYWluSW5wdXQoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJlLmcuLCBleGFtcGxlLmNvbSBvciAqLmV4YW1wbGUuY29tXCJcbiAgICAgICAgICAgICAgICAgIG9uS2V5UHJlc3M9eyhlKSA9PiBlLmtleSA9PT0gJ0VudGVyJyAmJiAoZS5wcmV2ZW50RGVmYXVsdCgpLCBhZGREb21haW4oKSl9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8QnV0dG9uIHR5cGU9XCJidXR0b25cIiBvbkNsaWNrPXthZGREb21haW59IHNpemU9XCJzbVwiPlxuICAgICAgICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICB7Zm9ybURhdGEuYWxsb3dlZF9kb21haW5zLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIHtmb3JtRGF0YS5hbGxvd2VkX2RvbWFpbnMubWFwKChkb21haW4pID0+IChcbiAgICAgICAgICAgICAgICAgICAgPEJhZGdlIGtleT17ZG9tYWlufSB2YXJpYW50PVwic2Vjb25kYXJ5XCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxHbG9iZSBjbGFzc05hbWU9XCJoLTMgdy0zIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIHtkb21haW59XG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJlbW92ZURvbWFpbihkb21haW4pfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWwtMSBoLTMgdy0zIHAtMFwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwiaC0yIHctMlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPFNlcGFyYXRvciAvPlxuXG4gICAgICAgICAgey8qIEV4cGlyYXRpb24gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZXhwaXJlc19hdFwiPkV4cGlyYXRpb24gRGF0ZSAoT3B0aW9uYWwpPC9MYWJlbD5cbiAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgaWQ9XCJleHBpcmVzX2F0XCJcbiAgICAgICAgICAgICAgICB0eXBlPVwiZGF0ZXRpbWUtbG9jYWxcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5leHBpcmVzX2F0fVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBleHBpcmVzX2F0OiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgbWluPXtuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTYpfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8RGlhbG9nRm9vdGVyPlxuICAgICAgICAgICAgPEJ1dHRvbiB0eXBlPVwiYnV0dG9uXCIgdmFyaWFudD1cIm91dGxpbmVcIiBvbkNsaWNrPXsoKSA9PiBvbk9wZW5DaGFuZ2UoZmFsc2UpfT5cbiAgICAgICAgICAgICAgQ2FuY2VsXG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDxCdXR0b24gdHlwZT1cInN1Ym1pdFwiIGRpc2FibGVkPXt1cGRhdGluZ30+XG4gICAgICAgICAgICAgIHt1cGRhdGluZyA/ICdVcGRhdGluZy4uLicgOiAnVXBkYXRlIEFQSSBLZXknfVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9EaWFsb2dGb290ZXI+XG4gICAgICAgIDwvZm9ybT5cbiAgICAgIDwvRGlhbG9nQ29udGVudD5cbiAgICA8L0RpYWxvZz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiRGlhbG9nIiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ0Rlc2NyaXB0aW9uIiwiRGlhbG9nRm9vdGVyIiwiRGlhbG9nSGVhZGVyIiwiRGlhbG9nVGl0bGUiLCJCdXR0b24iLCJJbnB1dCIsIkxhYmVsIiwiQ2hlY2tib3giLCJCYWRnZSIsIlNlcGFyYXRvciIsIlNldHRpbmdzIiwiU2hpZWxkIiwiR2xvYmUiLCJQbHVzIiwiWCIsInRvYXN0IiwiRWRpdEFwaUtleURpYWxvZyIsIm9wZW4iLCJvbk9wZW5DaGFuZ2UiLCJhcGlLZXkiLCJvblVwZGF0ZUFwaUtleSIsInVwZGF0aW5nIiwic2V0VXBkYXRpbmciLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwia2V5X25hbWUiLCJwZXJtaXNzaW9ucyIsImNoYXQiLCJzdHJlYW1pbmciLCJhbGxfbW9kZWxzIiwiYWxsb3dlZF9pcHMiLCJhbGxvd2VkX2RvbWFpbnMiLCJzdGF0dXMiLCJleHBpcmVzX2F0IiwiaXBJbnB1dCIsInNldElwSW5wdXQiLCJkb21haW5JbnB1dCIsInNldERvbWFpbklucHV0IiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwic2xpY2UiLCJoYW5kbGVTdWJtaXQiLCJlIiwicHJldmVudERlZmF1bHQiLCJ0cmltIiwiZXJyb3IiLCJhZGRJcEFkZHJlc3MiLCJpbmNsdWRlcyIsInByZXYiLCJyZW1vdmVJcEFkZHJlc3MiLCJpcCIsImZpbHRlciIsImkiLCJhZGREb21haW4iLCJyZW1vdmVEb21haW4iLCJkb21haW4iLCJkIiwiY2xhc3NOYW1lIiwiZm9ybSIsIm9uU3VibWl0IiwiZGl2IiwiaHRtbEZvciIsImlkIiwidmFsdWUiLCJvbkNoYW5nZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwicmVxdWlyZWQiLCJzZWxlY3QiLCJvcHRpb24iLCJjaGVja2VkIiwib25DaGVja2VkQ2hhbmdlIiwidmFyaWFudCIsIm9uS2V5UHJlc3MiLCJrZXkiLCJ0eXBlIiwib25DbGljayIsInNpemUiLCJsZW5ndGgiLCJtYXAiLCJtaW4iLCJkaXNhYmxlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/EditApiKeyDialog.tsx\n"));

/***/ })

});