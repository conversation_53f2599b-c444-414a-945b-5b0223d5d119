'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Activity, 
  Calendar, 
  Clock, 
  TrendingUp, 
  AlertCircle,
  RefreshCw,
  Download
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface ApiKeyUsageDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  apiKey: any;
}

interface UsageStats {
  total_requests: number;
  requests_today: number;
  requests_this_hour: number;
  requests_this_minute: number;
  avg_response_time: number;
  success_rate: number;
  most_used_model: string;
  most_used_provider: string;
  total_tokens: number;
  estimated_cost: number;
}

interface UsageLog {
  id: string;
  request_timestamp: string;
  endpoint: string;
  http_method: string;
  status_code: number;
  model_used?: string;
  provider_used?: string;
  tokens_prompt?: number;
  tokens_completion?: number;
  response_time_ms?: number;
  error_message?: string;
  ip_address?: string;
}

export function ApiKeyUsageDialog({
  open,
  onOpenChange,
  apiKey
}: ApiKeyUsageDialogProps) {
  const [loading, setLoading] = useState(true);
  const [usageStats, setUsageStats] = useState<UsageStats | null>(null);
  const [usageLogs, setUsageLogs] = useState<UsageLog[]>([]);
  const [timeRange, setTimeRange] = useState<'24h' | '7d' | '30d'>('24h');

  const fetchUsageData = async () => {
    if (!apiKey?.id) return;

    try {
      setLoading(true);
      
      // Fetch usage statistics
      const statsResponse = await fetch(`/api/user-api-keys/${apiKey.id}/usage/stats?range=${timeRange}`);
      if (statsResponse.ok) {
        const stats = await statsResponse.json();
        setUsageStats(stats);
      }

      // Fetch recent usage logs
      const logsResponse = await fetch(`/api/user-api-keys/${apiKey.id}/usage/logs?limit=50&range=${timeRange}`);
      if (logsResponse.ok) {
        const logs = await logsResponse.json();
        setUsageLogs(logs.usage_logs || []);
      }
    } catch (error) {
      console.error('Error fetching usage data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open && apiKey?.id) {
      fetchUsageData();
    }
  }, [open, apiKey?.id, timeRange]);

  const getStatusColor = (statusCode: number) => {
    if (statusCode >= 200 && statusCode < 300) return 'text-green-600';
    if (statusCode >= 400 && statusCode < 500) return 'text-yellow-600';
    if (statusCode >= 500) return 'text-red-600';
    return 'text-gray-600';
  };

  const getStatusBadgeColor = (statusCode: number) => {
    if (statusCode >= 200 && statusCode < 300) return 'bg-green-100 text-green-800 border-green-200';
    if (statusCode >= 400 && statusCode < 500) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    if (statusCode >= 500) return 'bg-red-100 text-red-800 border-red-200';
    return 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const exportUsageData = async () => {
    try {
      const response = await fetch(`/api/user-api-keys/${apiKey.id}/usage/export?range=${timeRange}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `api-key-usage-${apiKey.key_name}-${timeRange}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error exporting usage data:', error);
    }
  };

  if (loading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              API Key Usage
            </DialogTitle>
          </DialogHeader>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            Loading usage data...
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            API Key Usage: {apiKey.key_name}
          </DialogTitle>
          <DialogDescription>
            Usage statistics and logs for your API key
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Time Range Selector */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant={timeRange === '24h' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setTimeRange('24h')}
              >
                Last 24 Hours
              </Button>
              <Button
                variant={timeRange === '7d' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setTimeRange('7d')}
              >
                Last 7 Days
              </Button>
              <Button
                variant={timeRange === '30d' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setTimeRange('30d')}
              >
                Last 30 Days
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={fetchUsageData}>
                <RefreshCw className="h-4 w-4 mr-1" />
                Refresh
              </Button>
              <Button variant="outline" size="sm" onClick={exportUsageData}>
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>
            </div>
          </div>

          {/* Usage Statistics */}
          {usageStats && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Total Requests</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{usageStats.total_requests.toLocaleString()}</div>
                  <p className="text-xs text-gray-600">
                    {usageStats.requests_today} today
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Success Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{usageStats.success_rate.toFixed(1)}%</div>
                  <p className="text-xs text-gray-600">
                    HTTP 2xx responses
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Avg Response Time</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{usageStats.avg_response_time}ms</div>
                  <p className="text-xs text-gray-600">
                    Average latency
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Total Tokens</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{usageStats.total_tokens.toLocaleString()}</div>
                  <p className="text-xs text-gray-600">
                    ${usageStats.estimated_cost.toFixed(4)} estimated
                  </p>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Rate Limit Status */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Current Rate Limit Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-sm text-blue-700 mb-1">Per Minute</div>
                  <div className="text-lg font-semibold text-blue-900">
                    {usageStats?.requests_this_minute || 0} / {apiKey.rate_limit_per_minute}
                  </div>
                  <div className="w-full bg-blue-200 rounded-full h-2 mt-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ 
                        width: `${Math.min(100, ((usageStats?.requests_this_minute || 0) / apiKey.rate_limit_per_minute) * 100)}%` 
                      }}
                    ></div>
                  </div>
                </div>
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-sm text-blue-700 mb-1">Per Hour</div>
                  <div className="text-lg font-semibold text-blue-900">
                    {usageStats?.requests_this_hour || 0} / {apiKey.rate_limit_per_hour}
                  </div>
                  <div className="w-full bg-blue-200 rounded-full h-2 mt-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ 
                        width: `${Math.min(100, ((usageStats?.requests_this_hour || 0) / apiKey.rate_limit_per_hour) * 100)}%` 
                      }}
                    ></div>
                  </div>
                </div>
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-sm text-blue-700 mb-1">Per Day</div>
                  <div className="text-lg font-semibold text-blue-900">
                    {usageStats?.requests_today || 0} / {apiKey.rate_limit_per_day}
                  </div>
                  <div className="w-full bg-blue-200 rounded-full h-2 mt-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ 
                        width: `${Math.min(100, ((usageStats?.requests_today || 0) / apiKey.rate_limit_per_day) * 100)}%` 
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Usage Logs */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Recent Requests
              </CardTitle>
            </CardHeader>
            <CardContent>
              {usageLogs.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                  No usage logs found for the selected time range
                </div>
              ) : (
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {usageLogs.map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                      <div className="flex items-center gap-3">
                        <Badge className={getStatusBadgeColor(log.status_code)}>
                          {log.status_code}
                        </Badge>
                        <div>
                          <div className="font-medium text-sm">
                            {log.http_method} {log.endpoint}
                          </div>
                          <div className="text-xs text-gray-600">
                            {formatDistanceToNow(new Date(log.request_timestamp), { addSuffix: true })}
                            {log.ip_address && ` • ${log.ip_address}`}
                          </div>
                        </div>
                      </div>
                      <div className="text-right text-xs text-gray-600">
                        {log.model_used && (
                          <div>Model: {log.model_used}</div>
                        )}
                        {log.response_time_ms && (
                          <div>{log.response_time_ms}ms</div>
                        )}
                        {log.tokens_prompt && log.tokens_completion && (
                          <div>{(log.tokens_prompt + log.tokens_completion).toLocaleString()} tokens</div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
