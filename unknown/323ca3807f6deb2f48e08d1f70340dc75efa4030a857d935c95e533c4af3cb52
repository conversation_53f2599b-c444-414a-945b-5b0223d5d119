/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stripe/subscription-status/route";
exports.ids = ["app/api/stripe/subscription-status/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fsubscription-status%2Froute&page=%2Fapi%2Fstripe%2Fsubscription-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fsubscription-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fsubscription-status%2Froute&page=%2Fapi%2Fstripe%2Fsubscription-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fsubscription-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_stripe_subscription_status_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/stripe/subscription-status/route.ts */ \"(rsc)/./src/app/api/stripe/subscription-status/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stripe/subscription-status/route\",\n        pathname: \"/api/stripe/subscription-status\",\n        filename: \"route\",\n        bundlePath: \"app/api/stripe/subscription-status/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\stripe\\\\subscription-status\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_stripe_subscription_status_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZzdHJpcGUlMkZzdWJzY3JpcHRpb24tc3RhdHVzJTJGcm91dGUmcGFnZT0lMkZhcGklMkZzdHJpcGUlMkZzdWJzY3JpcHRpb24tc3RhdHVzJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGc3RyaXBlJTJGc3Vic2NyaXB0aW9uLXN0YXR1cyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDUm9LZXklMjBBcHAlNUNyb2tleS1hcHAlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNSb0tleSUyMEFwcCU1Q3Jva2V5LWFwcCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDOEI7QUFDM0c7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFJvS2V5IEFwcFxcXFxyb2tleS1hcHBcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcc3RyaXBlXFxcXHN1YnNjcmlwdGlvbi1zdGF0dXNcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3N0cmlwZS9zdWJzY3JpcHRpb24tc3RhdHVzL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvc3RyaXBlL3N1YnNjcmlwdGlvbi1zdGF0dXNcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3N0cmlwZS9zdWJzY3JpcHRpb24tc3RhdHVzL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcUm9LZXkgQXBwXFxcXHJva2V5LWFwcFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxzdHJpcGVcXFxcc3Vic2NyaXB0aW9uLXN0YXR1c1xcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fsubscription-status%2Froute&page=%2Fapi%2Fstripe%2Fsubscription-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fsubscription-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/stripe/subscription-status/route.ts":
/*!*********************************************************!*\
  !*** ./src/app/api/stripe/subscription-status/route.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\nconst supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\nasync function GET(req) {\n    try {\n        const { searchParams } = new URL(req.url);\n        const userId = searchParams.get('userId');\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing userId parameter'\n            }, {\n                status: 400\n            });\n        }\n        // Get user's subscription and profile\n        const { data: subscription, error: subscriptionError } = await supabase.from('subscriptions').select('*').eq('user_id', userId).order('created_at', {\n            ascending: false\n        }).limit(1).single();\n        const { data: profile, error: profileError } = await supabase.from('user_profiles').select('subscription_tier').eq('id', userId).single();\n        if (profileError) {\n            console.error('Error fetching user profile:', profileError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'User profile not found'\n            }, {\n                status: 404\n            });\n        }\n        // If no subscription found, user is on default starter tier\n        if (subscriptionError || !subscription) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                hasActiveSubscription: false,\n                tier: profile.subscription_tier || 'starter',\n                status: null,\n                currentPeriodEnd: null,\n                cancelAtPeriodEnd: false\n            });\n        }\n        // Check if subscription is active\n        const isActive = subscription.status === 'active';\n        const currentPeriodEnd = new Date(subscription.current_period_end);\n        const isExpired = currentPeriodEnd < new Date();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            hasActiveSubscription: isActive && !isExpired,\n            tier: subscription.tier,\n            status: subscription.status,\n            currentPeriodEnd: subscription.current_period_end,\n            currentPeriodStart: subscription.current_period_start,\n            cancelAtPeriodEnd: subscription.cancel_at_period_end,\n            stripeCustomerId: subscription.stripe_customer_id,\n            stripeSubscriptionId: subscription.stripe_subscription_id\n        });\n    } catch (error) {\n        console.error('Error fetching subscription status:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(req) {\n    try {\n        const { userId } = await req.json();\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing userId'\n            }, {\n                status: 400\n            });\n        }\n        // Get current usage for the user\n        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format\n        const { data: usage, error: usageError } = await supabase.from('usage_tracking').select('*').eq('user_id', userId).eq('month_year', currentMonth).single();\n        // Get user's current tier\n        const { data: profile } = await supabase.from('user_profiles').select('subscription_tier').eq('id', userId).single();\n        const tier = profile?.subscription_tier || 'starter';\n        // Get configuration and API key counts\n        const { count: configCount } = await supabase.from('custom_api_configs').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        const { count: apiKeyCount } = await supabase.from('api_keys').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        // Calculate tier limits\n        const limits = getTierLimits(tier);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            tier,\n            usage: {\n                configurations: configCount || 0,\n                apiKeys: apiKeyCount || 0,\n                apiRequests: usage?.api_requests_count || 0\n            },\n            limits,\n            canCreateConfig: (configCount || 0) < limits.configurations,\n            canCreateApiKey: (apiKeyCount || 0) < limits.apiKeysPerConfig\n        });\n    } catch (error) {\n        console.error('Error fetching usage status:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nfunction getTierLimits(tier) {\n    switch(tier){\n        case 'starter':\n            return {\n                configurations: 5,\n                apiKeysPerConfig: 25,\n                apiRequests: 999999\n            };\n        case 'professional':\n            return {\n                configurations: 25,\n                apiKeysPerConfig: 100,\n                apiRequests: 999999\n            };\n        case 'enterprise':\n            return {\n                configurations: 999999,\n                apiKeysPerConfig: 999999,\n                apiRequests: 999999\n            };\n        default:\n            return {\n                configurations: 5,\n                apiKeysPerConfig: 25,\n                apiRequests: 999999\n            };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stripe/subscription-status/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fsubscription-status%2Froute&page=%2Fapi%2Fstripe%2Fsubscription-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fsubscription-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();