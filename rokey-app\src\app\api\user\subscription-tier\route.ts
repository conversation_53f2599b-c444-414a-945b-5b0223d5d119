import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';

// GET /api/user/subscription-tier - Get user's current subscription tier
export async function GET(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  try {
    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's active subscription tier
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('tier')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    const tier = subscription?.tier || 'starter';

    return NextResponse.json({ tier });

  } catch (error) {
    console.error('Error in GET /api/user/subscription-tier:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
