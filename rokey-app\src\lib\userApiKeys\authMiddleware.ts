import { type NextRequest } from 'next/server';
import { ApiKeyValidator } from './apiKeyValidator';
import type { UserGeneratedApiKey, ApiKeyValidationResult } from '@/types/userApiKeys';

export interface AuthenticatedRequest extends NextRequest {
  userApiKey?: UserGeneratedApiKey;
  userConfig?: {
    id: string;
    name: string;
    user_id: string;
    routing_strategy: string;
    routing_strategy_params: any;
  };
  rateLimitStatus?: ApiKeyValidationResult['rateLimitStatus'];
}

export class ApiKeyAuthMiddleware {
  private validator: ApiKeyValidator;

  constructor() {
    this.validator = new ApiKeyValidator();
  }

  /**
   * Extracts API key from request headers
   * Supports both Authorization header and x-api-key header
   * @param request The incoming request
   * @returns The API key or null if not found
   */
  private extractApiKey(request: NextRequest): string | null {
    // Check Authorization header (Bearer token format)
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      const match = authHeader.match(/^Bearer\s+(.+)$/i);
      if (match) {
        return match[1];
      }
    }

    // Check x-api-key header
    const apiKeyHeader = request.headers.get('x-api-key');
    if (apiKeyHeader) {
      return apiKeyHeader;
    }

    return null;
  }

  /**
   * Gets the client IP address from the request
   * @param request The incoming request
   * @returns The IP address
   */
  private getClientIp(request: NextRequest): string {
    // Check various headers for the real IP
    const forwardedFor = request.headers.get('x-forwarded-for');
    if (forwardedFor) {
      return forwardedFor.split(',')[0].trim();
    }

    const realIp = request.headers.get('x-real-ip');
    if (realIp) {
      return realIp;
    }

    const cfConnectingIp = request.headers.get('cf-connecting-ip');
    if (cfConnectingIp) {
      return cfConnectingIp;
    }

    // Fallback to a default IP if none found
    return '127.0.0.1';
  }

  /**
   * Authenticates a request using user-generated API key
   * @param request The incoming request
   * @returns Authentication result with user data and config
   */
  async authenticateRequest(request: NextRequest): Promise<{
    success: boolean;
    error?: string;
    statusCode?: number;
    userApiKey?: UserGeneratedApiKey;
    userConfig?: any;
    rateLimitStatus?: any;
    ipAddress?: string;
  }> {
    try {
      // Extract API key from request
      const apiKey = this.extractApiKey(request);
      if (!apiKey) {
        return {
          success: false,
          error: 'API key is required. Provide it in Authorization header as "Bearer YOUR_API_KEY" or in x-api-key header.',
          statusCode: 401
        };
      }

      // Get client IP
      const ipAddress = this.getClientIp(request);

      // Validate the API key
      const validationResult = await this.validator.validateApiKey(apiKey, ipAddress);

      if (!validationResult.isValid) {
        let statusCode = 401;
        
        // Set appropriate status code based on error type
        if (validationResult.rateLimitExceeded) {
          statusCode = 429;
        } else if (validationResult.error?.includes('expired')) {
          statusCode = 401;
        } else if (validationResult.error?.includes('IP address not allowed')) {
          statusCode = 403;
        }

        return {
          success: false,
          error: validationResult.error || 'Invalid API key',
          statusCode,
          rateLimitStatus: validationResult.rateLimitStatus
        };
      }

      // Increment rate limit counters
      await this.validator.incrementRateLimitCounters(validationResult.apiKey!.id);

      return {
        success: true,
        userApiKey: validationResult.apiKey!,
        userConfig: (validationResult.apiKey as any).custom_api_configs,
        rateLimitStatus: validationResult.rateLimitStatus,
        ipAddress
      };

    } catch (error) {
      console.error('Error in API key authentication:', error);
      return {
        success: false,
        error: 'Internal authentication error',
        statusCode: 500
      };
    }
  }

  /**
   * Logs API usage for monitoring and analytics
   * @param userApiKey The authenticated API key
   * @param request The request object
   * @param response Response details
   */
  async logApiUsage(
    userApiKey: UserGeneratedApiKey,
    request: NextRequest,
    response: {
      statusCode: number;
      modelUsed?: string;
      providerUsed?: string;
      tokensPrompt?: number;
      tokensCompletion?: number;
      costUsd?: number;
      responseTimeMs?: number;
      errorMessage?: string;
      errorType?: string;
    },
    ipAddress?: string
  ): Promise<void> {
    try {
      const url = new URL(request.url);
      
      await this.validator.logApiUsage(
        userApiKey.id,
        userApiKey.user_id,
        userApiKey.custom_api_config_id,
        {
          endpoint: url.pathname,
          method: request.method,
          statusCode: response.statusCode,
          ipAddress,
          userAgent: request.headers.get('user-agent') || undefined,
          referer: request.headers.get('referer') || undefined,
          modelUsed: response.modelUsed,
          providerUsed: response.providerUsed,
          tokensPrompt: response.tokensPrompt,
          tokensCompletion: response.tokensCompletion,
          costUsd: response.costUsd,
          responseTimeMs: response.responseTimeMs,
          errorMessage: response.errorMessage,
          errorType: response.errorType
        }
      );
    } catch (error) {
      console.error('Error logging API usage:', error);
      // Don't throw here as this shouldn't break the main request
    }
  }

  /**
   * Checks if the API key has permission for a specific operation
   * @param userApiKey The API key to check
   * @param operation The operation to check (e.g., 'chat', 'streaming')
   * @returns True if permission is granted
   */
  hasPermission(userApiKey: UserGeneratedApiKey, operation: string): boolean {
    const permissions = userApiKey.permissions;
    
    switch (operation) {
      case 'chat':
        return permissions.chat === true;
      case 'streaming':
        return permissions.streaming === true;
      case 'all_models':
        return permissions.all_models === true;
      default:
        return false;
    }
  }

  /**
   * Validates CORS origin against allowed domains
   * @param userApiKey The API key to check
   * @param origin The request origin
   * @returns True if origin is allowed
   */
  isOriginAllowed(userApiKey: UserGeneratedApiKey, origin: string | null): boolean {
    if (!origin) return true; // Allow requests without origin (e.g., server-to-server)
    
    const allowedDomains = userApiKey.allowed_domains;
    if (!allowedDomains || allowedDomains.length === 0) {
      return true; // No restrictions
    }

    // Check if origin matches any allowed domain
    return allowedDomains.some(domain => {
      if (domain === '*') return true;
      if (domain.startsWith('*.')) {
        // Wildcard subdomain matching
        const baseDomain = domain.slice(2);
        return origin.endsWith(baseDomain);
      }
      return origin === domain || origin === `https://${domain}` || origin === `http://${domain}`;
    });
  }

  /**
   * Creates a standardized error response for API key authentication failures
   * @param error The error message
   * @param statusCode The HTTP status code
   * @param rateLimitStatus Optional rate limit status for 429 errors
   * @returns Response object
   */
  createErrorResponse(
    error: string,
    statusCode: number,
    rateLimitStatus?: any
  ): Response {
    const body: any = {
      error: {
        message: error,
        type: this.getErrorType(statusCode),
        code: statusCode
      }
    };

    // Add rate limit headers for 429 responses
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (statusCode === 429 && rateLimitStatus) {
      headers['X-RateLimit-Limit-Minute'] = rateLimitStatus.minute?.limit_value?.toString() || '0';
      headers['X-RateLimit-Remaining-Minute'] = Math.max(0, (rateLimitStatus.minute?.limit_value || 0) - (rateLimitStatus.minute?.current_count || 0)).toString();
      headers['X-RateLimit-Reset-Minute'] = new Date(Date.now() + 60000).toISOString();
      
      headers['X-RateLimit-Limit-Hour'] = rateLimitStatus.hour?.limit_value?.toString() || '0';
      headers['X-RateLimit-Remaining-Hour'] = Math.max(0, (rateLimitStatus.hour?.limit_value || 0) - (rateLimitStatus.hour?.current_count || 0)).toString();
      headers['X-RateLimit-Reset-Hour'] = new Date(Date.now() + 3600000).toISOString();
    }

    return new Response(JSON.stringify(body), {
      status: statusCode,
      headers
    });
  }

  /**
   * Gets the error type based on status code
   * @param statusCode The HTTP status code
   * @returns Error type string
   */
  private getErrorType(statusCode: number): string {
    switch (statusCode) {
      case 401:
        return 'authentication_error';
      case 403:
        return 'permission_denied';
      case 429:
        return 'rate_limit_exceeded';
      case 500:
        return 'internal_error';
      default:
        return 'api_error';
    }
  }
}
