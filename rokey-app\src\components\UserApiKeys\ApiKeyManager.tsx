'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Key, AlertCircle, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { ApiKeyCard } from './ApiKeyCard';
import { CreateApiKeyDialog } from './CreateApiKeyDialog';
import { EditApiKeyDialog } from './EditApiKeyDialog';
import { ApiKeyUsageDialog } from './ApiKeyUsageDialog';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ApiKeyManagerProps {
  configId: string;
  configName: string;
}

export function ApiKeyManager({ configId, configName }: ApiKeyManagerProps) {
  const [apiKeys, setApiKeys] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showUsageDialog, setShowUsageDialog] = useState(false);
  const [selectedApiKey, setSelectedApiKey] = useState<any>(null);
  const [subscriptionInfo, setSubscriptionInfo] = useState<{
    tier: string;
    keyLimit: number;
    currentCount: number;
  } | null>(null);

  // Fetch API keys for this configuration
  const fetchApiKeys = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/user-api-keys?config_id=${configId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch API keys');
      }

      const data = await response.json();
      setApiKeys(data.api_keys || []);
    } catch (error) {
      console.error('Error fetching API keys:', error);
      toast.error('Failed to load API keys');
    } finally {
      setLoading(false);
    }
  };

  // Fetch subscription info
  const fetchSubscriptionInfo = async () => {
    try {
      // Get user profile which includes subscription tier
      const response = await fetch('/api/user/profile');
      if (response.ok) {
        const profile = await response.json();
        // Get the tier from the user's active subscription
        const tierResponse = await fetch('/api/user/subscription-tier');
        const tierData = tierResponse.ok ? await tierResponse.json() : null;
        const tier = tierData?.tier || profile.subscription_tier || 'starter';

        const limits = {
          starter: 2,
          professional: 10,
          enterprise: 50
        };

        setSubscriptionInfo({
          tier,
          keyLimit: limits[tier as keyof typeof limits] || limits.starter,
          currentCount: apiKeys.length
        });
      }
    } catch (error) {
      console.error('Error fetching subscription info:', error);
      // Fallback to starter tier
      setSubscriptionInfo({
        tier: 'starter',
        keyLimit: 2,
        currentCount: apiKeys.length
      });
    }
  };

  useEffect(() => {
    fetchApiKeys();
  }, [configId]);

  useEffect(() => {
    if (apiKeys.length >= 0) {
      fetchSubscriptionInfo();
    }
  }, [apiKeys]);

  const handleCreateApiKey = async (keyData: any) => {
    try {
      setCreating(true);
      const response = await fetch('/api/user-api-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...keyData,
          custom_api_config_id: configId,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create API key');
      }

      const newApiKey = await response.json();
      
      // Show the full API key in a special dialog since it's only shown once
      toast.success('API key created successfully!');
      
      // Refresh the list
      await fetchApiKeys();
      setShowCreateDialog(false);
      
      return newApiKey;
    } catch (error: any) {
      console.error('Error creating API key:', error);
      toast.error(error.message || 'Failed to create API key');
      throw error;
    } finally {
      setCreating(false);
    }
  };

  const handleEditApiKey = async (keyId: string, updates: any) => {
    try {
      const response = await fetch(`/api/user-api-keys/${keyId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update API key');
      }

      toast.success('API key updated successfully');
      await fetchApiKeys();
      setShowEditDialog(false);
      setSelectedApiKey(null);
    } catch (error: any) {
      console.error('Error updating API key:', error);
      toast.error(error.message || 'Failed to update API key');
    }
  };

  const handleRevokeApiKey = async (keyId: string) => {
    if (!confirm('Are you sure you want to revoke this API key? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/user-api-keys/${keyId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to revoke API key');
      }

      toast.success('API key revoked successfully');
      await fetchApiKeys();
    } catch (error: any) {
      console.error('Error revoking API key:', error);
      toast.error(error.message || 'Failed to revoke API key');
    }
  };

  const handleViewUsage = (keyId: string) => {
    const apiKey = apiKeys.find(key => key.id === keyId);
    setSelectedApiKey(apiKey);
    setShowUsageDialog(true);
  };

  const canCreateMoreKeys = subscriptionInfo 
    ? subscriptionInfo.currentCount < subscriptionInfo.keyLimit 
    : true;

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
          Loading API keys...
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Key className="h-6 w-6" />
            API Keys
          </h2>
          <p className="text-gray-600 mt-1">
            Generate API keys for programmatic access to {configName}
          </p>
        </div>
        <Button
          onClick={() => setShowCreateDialog(true)}
          disabled={!canCreateMoreKeys}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Create API Key
        </Button>
      </div>

      {/* Subscription Info */}
      {subscriptionInfo && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            You are on the <Badge variant="secondary">{subscriptionInfo.tier}</Badge> plan.
            You have used {subscriptionInfo.currentCount} of {subscriptionInfo.keyLimit} API keys.
            {!canCreateMoreKeys && (
              <span className="text-red-600 ml-2">
                Upgrade your plan to create more API keys.
              </span>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* API Keys List */}
      {apiKeys.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Key className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold mb-2">No API Keys</h3>
            <p className="text-gray-600 mb-4">
              Create your first API key to start using the RouKey API programmatically.
            </p>
            <Button
              onClick={() => setShowCreateDialog(true)}
              disabled={!canCreateMoreKeys}
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Your First API Key
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {apiKeys.map((apiKey) => (
            <ApiKeyCard
              key={apiKey.id}
              apiKey={apiKey}
              onEdit={(key) => {
                setSelectedApiKey(key);
                setShowEditDialog(true);
              }}
              onRevoke={handleRevokeApiKey}
              onViewUsage={handleViewUsage}
            />
          ))}
        </div>
      )}

      {/* Dialogs */}
      <CreateApiKeyDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onCreateApiKey={handleCreateApiKey}
        configName={configName}
        creating={creating}
        subscriptionTier={subscriptionInfo?.tier || 'starter'}
      />

      {selectedApiKey && (
        <>
          <EditApiKeyDialog
            open={showEditDialog}
            onOpenChange={setShowEditDialog}
            apiKey={selectedApiKey}
            onUpdateApiKey={(updates) => handleEditApiKey(selectedApiKey.id, updates)}
          />

          <ApiKeyUsageDialog
            open={showUsageDialog}
            onOpenChange={setShowUsageDialog}
            apiKey={selectedApiKey}
          />
        </>
      )}
    </div>
  );
}
