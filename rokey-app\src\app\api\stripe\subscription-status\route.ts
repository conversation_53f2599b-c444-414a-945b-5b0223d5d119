import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId parameter' },
        { status: 400 }
      );
    }

    // Get user's subscription and profile
    const { data: subscription, error: subscriptionError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('subscription_tier')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 }
      );
    }

    // If no subscription found, user is on default starter tier
    if (subscriptionError || !subscription) {
      return NextResponse.json({
        hasActiveSubscription: false,
        tier: profile.subscription_tier || 'starter',
        status: null,
        currentPeriodEnd: null,
        cancelAtPeriodEnd: false,
      });
    }

    // Check if subscription is active
    const isActive = subscription.status === 'active';
    const currentPeriodEnd = new Date(subscription.current_period_end);
    const isExpired = currentPeriodEnd < new Date();

    return NextResponse.json({
      hasActiveSubscription: isActive && !isExpired,
      tier: subscription.tier,
      status: subscription.status,
      currentPeriodEnd: subscription.current_period_end,
      currentPeriodStart: subscription.current_period_start,
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      stripeCustomerId: subscription.stripe_customer_id,
      stripeSubscriptionId: subscription.stripe_subscription_id,
    });

  } catch (error) {
    console.error('Error fetching subscription status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const { userId } = await req.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId' },
        { status: 400 }
      );
    }

    // Get current usage for the user
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format

    const { data: usage, error: usageError } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', userId)
      .eq('month_year', currentMonth)
      .single();

    // Get user's current tier
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('subscription_tier')
      .eq('id', userId)
      .single();

    const tier = profile?.subscription_tier || 'starter';

    // Get configuration and API key counts
    const { count: configCount } = await supabase
      .from('custom_api_configs')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    const { count: apiKeyCount } = await supabase
      .from('api_keys')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    // Calculate tier limits
    const limits = getTierLimits(tier);

    return NextResponse.json({
      tier,
      usage: {
        configurations: configCount || 0,
        apiKeys: apiKeyCount || 0,
        apiRequests: usage?.api_requests_count || 0,
      },
      limits,
      canCreateConfig: (configCount || 0) < limits.configurations,
      canCreateApiKey: (apiKeyCount || 0) < limits.apiKeysPerConfig,
    });

  } catch (error) {
    console.error('Error fetching usage status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function getTierLimits(tier: string) {
  switch (tier) {
    case 'starter':
      return {
        configurations: 5,
        apiKeysPerConfig: 25,
        apiRequests: 999999, // Unlimited
      };
    case 'professional':
      return {
        configurations: 25,
        apiKeysPerConfig: 100,
        apiRequests: 999999, // Unlimited
      };
    case 'enterprise':
      return {
        configurations: 999999, // Unlimited
        apiKeysPerConfig: 999999, // Unlimited
        apiRequests: 999999, // Unlimited
      };
    default:
      return {
        configurations: 5,
        apiKeysPerConfig: 25,
        apiRequests: 999999,
      };
  }
}
