"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/EditApiKeyDialog.tsx":
/*!*********************************************************!*\
  !*** ./src/components/UserApiKeys/EditApiKeyDialog.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditApiKeyDialog: () => (/* binding */ EditApiKeyDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Plus,Settings,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Plus,Settings,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Plus,Settings,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Plus,Settings,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Globe,Plus,Settings,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ EditApiKeyDialog auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction EditApiKeyDialog(param) {\n    let { open, onOpenChange, apiKey, onUpdateApiKey } = param;\n    _s();\n    const [updating, setUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        key_name: '',\n        permissions: {\n            chat: true,\n            streaming: true,\n            all_models: true\n        },\n        allowed_ips: [],\n        allowed_domains: [],\n        status: 'active',\n        expires_at: ''\n    });\n    const [ipInput, setIpInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [domainInput, setDomainInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Initialize form data when apiKey changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditApiKeyDialog.useEffect\": ()=>{\n            if (apiKey) {\n                setFormData({\n                    key_name: apiKey.key_name || '',\n                    permissions: apiKey.permissions || {\n                        chat: true,\n                        streaming: true,\n                        all_models: true\n                    },\n                    rate_limit_per_minute: apiKey.rate_limit_per_minute || 30,\n                    rate_limit_per_hour: apiKey.rate_limit_per_hour || 500,\n                    rate_limit_per_day: apiKey.rate_limit_per_day || 5000,\n                    allowed_ips: apiKey.allowed_ips || [],\n                    allowed_domains: apiKey.allowed_domains || [],\n                    status: apiKey.status || 'active',\n                    expires_at: apiKey.expires_at ? new Date(apiKey.expires_at).toISOString().slice(0, 16) : ''\n                });\n            }\n        }\n    }[\"EditApiKeyDialog.useEffect\"], [\n        apiKey\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.key_name.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error('Please enter a name for your API key');\n            return;\n        }\n        try {\n            setUpdating(true);\n            await onUpdateApiKey({\n                ...formData,\n                key_name: formData.key_name.trim(),\n                expires_at: formData.expires_at || null\n            });\n        } catch (error) {\n        // Error is handled in the parent component\n        } finally{\n            setUpdating(false);\n        }\n    };\n    const addIpAddress = ()=>{\n        if (ipInput.trim() && !formData.allowed_ips.includes(ipInput.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    allowed_ips: [\n                        ...prev.allowed_ips,\n                        ipInput.trim()\n                    ]\n                }));\n            setIpInput('');\n        }\n    };\n    const removeIpAddress = (ip)=>{\n        setFormData((prev)=>({\n                ...prev,\n                allowed_ips: prev.allowed_ips.filter((i)=>i !== ip)\n            }));\n    };\n    const addDomain = ()=>{\n        if (domainInput.trim() && !formData.allowed_domains.includes(domainInput.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    allowed_domains: [\n                        ...prev.allowed_domains,\n                        domainInput.trim()\n                    ]\n                }));\n            setDomainInput('');\n        }\n    };\n    const removeDomain = (domain)=>{\n        setFormData((prev)=>({\n                ...prev,\n                allowed_domains: prev.allowed_domains.filter((d)=>d !== domain)\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"max-w-2xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                \"Edit API Key\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                            children: \"Update the settings for your API key\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"key_name\",\n                                            children: \"API Key Name *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"key_name\",\n                                            value: formData.key_name,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        key_name: e.target.value\n                                                    })),\n                                            placeholder: \"e.g., Production API Key\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"status\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"status\",\n                                            value: formData.status,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        status: e.target.value\n                                                    })),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"active\",\n                                                    children: \"Active\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"inactive\",\n                                                    children: \"Inactive\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            className: \"text-base font-semibold\",\n                                            children: \"Permissions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                    id: \"chat\",\n                                                    checked: formData.permissions.chat,\n                                                    onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                permissions: {\n                                                                    ...prev.permissions,\n                                                                    chat: !!checked\n                                                                }\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"chat\",\n                                                    className: \"text-sm\",\n                                                    children: \"Chat Completions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: \"Required\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                    id: \"streaming\",\n                                                    checked: formData.permissions.streaming,\n                                                    onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                permissions: {\n                                                                    ...prev.permissions,\n                                                                    streaming: !!checked\n                                                                }\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"streaming\",\n                                                    className: \"text-sm\",\n                                                    children: \"Streaming Responses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                    id: \"all_models\",\n                                                    checked: formData.permissions.all_models,\n                                                    onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                permissions: {\n                                                                    ...prev.permissions,\n                                                                    all_models: !!checked\n                                                                }\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"all_models\",\n                                                    className: \"text-sm\",\n                                                    children: \"Access to All Models\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            className: \"text-base font-semibold\",\n                                            children: \"Security Restrictions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            children: \"Allowed IP Addresses\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    value: ipInput,\n                                                    onChange: (e)=>setIpInput(e.target.value),\n                                                    placeholder: \"e.g., *********** or 10.0.0.0/8\",\n                                                    onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addIpAddress())\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    type: \"button\",\n                                                    onClick: addIpAddress,\n                                                    size: \"sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this),\n                                        formData.allowed_ips.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: formData.allowed_ips.map((ip)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        ip,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeIpAddress(ip),\n                                                            className: \"ml-1 h-3 w-3 p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-2 w-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, ip, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            children: \"Allowed Domains (CORS)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    value: domainInput,\n                                                    onChange: (e)=>setDomainInput(e.target.value),\n                                                    placeholder: \"e.g., example.com or *.example.com\",\n                                                    onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addDomain())\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    type: \"button\",\n                                                    onClick: addDomain,\n                                                    size: \"sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this),\n                                        formData.allowed_domains.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: formData.allowed_domains.map((domain)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        domain,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeDomain(domain),\n                                                            className: \"ml-1 h-3 w-3 p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Globe_Plus_Settings_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-2 w-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, domain, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_8__.Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"expires_at\",\n                                        children: \"Expiration Date (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        id: \"expires_at\",\n                                        type: \"datetime-local\",\n                                        value: formData.expires_at,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    expires_at: e.target.value\n                                                })),\n                                        min: new Date().toISOString().slice(0, 16)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>onOpenChange(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"submit\",\n                                    disabled: updating,\n                                    children: updating ? 'Updating...' : 'Update API Key'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n            lineNumber: 137,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\EditApiKeyDialog.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, this);\n}\n_s(EditApiKeyDialog, \"SuclHHsvcs4TOMKffJq2XNuyEWA=\");\n_c = EditApiKeyDialog;\nvar _c;\n$RefreshReg$(_c, \"EditApiKeyDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/EditApiKeyDialog.tsx\n"));

/***/ })

});